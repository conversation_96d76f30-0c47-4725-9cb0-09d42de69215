'use client'

interface AuthButtonProps {
  label: string
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
  isLoading?: boolean
  disabled?: boolean
  variant?: 'primary' | 'secondary' | 'danger'
  className?: string
}

export default function AuthButton({
  label,
  onClick,
  type = 'button',
  isLoading = false,
  disabled = false,
  variant = 'primary',
  className = ''
}: AuthButtonProps) {
  const baseClasses = `
    w-full px-4 py-2.5 sm:py-2 text-base sm:text-sm font-medium rounded-md transition-colors
    focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50
    disabled:cursor-not-allowed flex items-center justify-center
    min-h-[44px] sm:min-h-[auto] touch-manipulation
  `
  
  const variantClasses = {
    primary: `
      bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500
      disabled:bg-blue-300
    `,
    secondary: `
      bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500
      disabled:bg-gray-100
    `,
    danger: `
      bg-red-600 text-white hover:bg-red-700 focus:ring-red-500
      disabled:bg-red-300
    `
  }
  
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {isLoading ? (
        <>
          <svg 
            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" 
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24"
          >
            <circle 
              className="opacity-25" 
              cx="12" 
              cy="12" 
              r="10" 
              stroke="currentColor" 
              strokeWidth="4"
            />
            <path 
              className="opacity-75" 
              fill="currentColor" 
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          处理中...
        </>
      ) : (
        label
      )}
    </button>
  )
}

'use client'

import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { useState } from 'react'
import { useIsMobile } from '@/hooks/useResponsive'
import TopUserStatusBar from './TopUserStatusBar'
import NavigationSidebar from './NavigationSidebar'
import MobileSidebarOverlay from './MobileSidebarOverlay'

interface AppLayoutProps {
  children: React.ReactNode
}

interface User {
  id: string
  email: string
  settings?: {
    baseCurrency?: {
      code: string
      name: string
      symbol: string
    }
  }
}

interface AppLayoutClientProps {
  children: React.ReactNode
  user: User
}

function AppLayoutClient({ children, user }: AppLayoutClientProps) {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
  const isMobile = useIsMobile()

  // 移动端侧边栏控制
  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen)
  }

  const closeMobileSidebar = () => {
    setIsMobileSidebarOpen(false)
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 顶部用户状态栏 */}
      <TopUserStatusBar
        user={user}
        onMenuClick={toggleMobileSidebar}
        showMenuButton={isMobile}
      />

      {/* 主内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 桌面端左侧导航栏 */}
        <div className={`${isMobile ? 'hidden' : 'block'}`}>
          <NavigationSidebar user={user} />
        </div>

        {/* 移动端侧边栏遮罩 */}
        {isMobile && (
          <MobileSidebarOverlay
            isOpen={isMobileSidebarOpen}
            onClose={closeMobileSidebar}
          >
            <NavigationSidebar
              user={user}
              isMobile={true}
              onNavigate={closeMobileSidebar}
            />
          </MobileSidebarOverlay>
        )}

        {/* 右侧主内容 */}
        <main className="flex-1 overflow-y-auto bg-white">
          {children}
        </main>
      </div>
    </div>
  )
}

export default async function AppLayout({ children }: AppLayoutProps) {
  // 强制执行身份验证
  const user = await getCurrentUser()
  if (!user) {
    redirect('/login')
  }

  // 获取用户设置
  const userSettings = await prisma.userSettings.findUnique({
    where: { userId: user.id },
    include: { baseCurrency: true }
  })

  const userWithSettings = {
    ...user,
    settings: userSettings ? {
      baseCurrency: userSettings.baseCurrency
    } : undefined
  }

  return (
    <AppLayoutClient user={userWithSettings}>
      {children}
    </AppLayoutClient>
  )
}
